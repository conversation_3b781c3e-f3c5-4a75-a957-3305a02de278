<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;

use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::query()->with('categories');

        // Search
        if ($search = $request->input('search')) {
            $query->where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%");
        }

        // Category filter
        if ($categoryId = $request->input('category_id')) {
            $query->whereHas('categories', function ($q) use ($categoryId) {
                $q->where('categories.id', $categoryId);
            });
        }

        // Price range filter
        if ($minPrice = $request->input('min_price')) {
            $query->where('price', '>=', $minPrice);
        }
        if ($maxPrice = $request->input('max_price')) {
            $query->where('price', '<=', $maxPrice);
        }

        // Sort
        $sort = $request->input('sort', 'created_at');
        $direction = $request->input('direction', 'desc');
        $query->orderBy($sort, $direction);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $products = $query->paginate($perPage)->withQueryString();

        // Get categories for filter dropdown
        $categories = Category::where('status', 'active')->pluck('name', 'id');

        return view('admin.products.index', compact('products', 'categories'));
    }

    public function create()
    {
        $categories = Category::where('status', 'active')->get();
        return view('admin.products.create', compact('categories'));
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|max:255|unique:products,name',
                'slug' => 'nullable|max:255|unique:products,slug',
                'description' => 'nullable|string',
                'youtube_video_url' => 'nullable|url',
                'price' => 'required|numeric|min:0',
                'categories' => 'required|array',
                'categories.*' => 'exists:categories,id',
                'images' => 'required|array',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                'primary_image' => 'required|integer|min:0',
            ]);

            DB::beginTransaction();

            // Create the product
            $product = Product::create([
                'name' => $validated['name'],
                'slug' => $validated['slug'] ?? \Str::slug($validated['name']),
                'description' => $validated['description'],
                'youtube_video_url' => $validated['youtube_video_url'] ?? null,
                'price' => $validated['price'],
            ]);

            // Attach categories
            $product->categories()->attach($validated['categories']);

            // Handle images
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $imageFile) {
                    $filename = 'product_' . time() . '_' . $index . '.' . $imageFile->getClientOriginalExtension();

                    // Create directory in public path if it doesn't exist
                    $publicPath = public_path('uploads/products');
                    if (!file_exists($publicPath)) {
                        mkdir($publicPath, 0755, true);
                    }

                    // Resize and save image directly to public directory
                    try {
                        $manager = new ImageManager(new Driver());
                        $img = $manager->read($imageFile->getRealPath());
                        $img->resize(500, 500);

                        // Save the image to public directory
                        $fullPath = $publicPath . '/' . $filename;
                        $img->save($fullPath);

                        $imagePath = 'uploads/products/' . $filename;

                        $product->images()->create([
                            'path' => $imagePath,
                            'original_name' => $imageFile->getClientOriginalName(),
                            'mime_type' => $imageFile->getMimeType(),
                            'size' => $imageFile->getSize(),
                            'is_primary' => $index == $request->input('primary_image'),
                        ]);
                    } catch (\Exception $e) {
                        // Fallback: save original image without resizing
                        $imageFile->move($publicPath, $filename);
                        $imagePath = 'uploads/products/' . $filename;

                        $product->images()->create([
                            'path' => $imagePath,
                            'original_name' => $imageFile->getClientOriginalName(),
                            'mime_type' => $imageFile->getMimeType(),
                            'size' => $imageFile->getSize(),
                            'is_primary' => $index == $request->input('primary_image'),
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'redirect' => route('products.index')
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error creating product: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Product $product)
    {
        return view('admin.products.show', compact('product'));
    }

    public function edit(Product $product)
    {
        $categories = Category::where('status', 'active')->get();
        return view('admin.products.edit', compact('product', 'categories'));
    }

    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => "required|max:255|unique:products,name,{$product->id}",
            'slug' => "nullable|max:255|unique:products,slug,{$product->id}",
            'description' => 'nullable|string',
            'youtube_video_url' => 'nullable|url',
            'price' => 'required|numeric|min:0',
            'categories' => 'required|array',
            'categories.*' => 'exists:categories,id',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'primary_image' => 'required_with:images|string', // Only required when uploading new images
            'remove_images' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $product->update([
                'name' => $validated['name'],
                'slug' => $validated['slug'],
                'description' => $validated['description'],
                'youtube_video_url' => $validated['youtube_video_url'] ?? null,
                'price' => $validated['price'],
            ]);

            // Handle categories
            $product->categories()->sync($validated['categories']);

            // Handle image deletions
            if ($request->input('remove_images')) {
                $removeImageIds = explode(',', $request->input('remove_images'));
                $imagesToDelete = $product->images()->whereIn('id', $removeImageIds)->get();

                foreach ($imagesToDelete as $image) {
                    // Delete file from public directory
                    if ($image->path && file_exists(public_path($image->path))) {
                        unlink(public_path($image->path));
                    }
                    $image->delete();
                }
            }

            // Reset all images to non-primary
            $product->images()->update(['is_primary' => false]);

            // Handle new image uploads
            if ($request->hasFile('images')) {
                // Set a default primary image if none is provided
                $primaryImageKey = $request->input('primary_image');
                if (empty($primaryImageKey) && count($request->file('images')) > 0) {
                    $primaryImageKey = 'new_0';
                }

                foreach ($request->file('images') as $index => $imageFile) {
                    $filename = 'product_' . time() . '_' . $index . '.' . $imageFile->getClientOriginalExtension();

                    // Create directory in public path if it doesn't exist
                    $publicPath = public_path('uploads/products');
                    if (!file_exists($publicPath)) {
                        mkdir($publicPath, 0755, true);
                    }

                    $isPrimary = $primaryImageKey === "new_{$index}";

                    // Resize and save image directly to public directory
                    try {
                        $manager = new ImageManager(new Driver());
                        $img = $manager->read($imageFile->getRealPath());
                        $img->resize(500, 500);

                        // Save the image to public directory
                        $fullPath = $publicPath . '/' . $filename;
                        $img->save($fullPath);

                        $imagePath = 'uploads/products/' . $filename;

                        $product->images()->create([
                            'path' => $imagePath,
                            'original_name' => $imageFile->getClientOriginalName(),
                            'mime_type' => $imageFile->getMimeType(),
                            'size' => $imageFile->getSize(),
                            'is_primary' => $isPrimary,
                        ]);
                    } catch (\Exception $e) {
                        // Fallback: save original image without resizing
                        $imageFile->move($publicPath, $filename);
                        $imagePath = 'uploads/products/' . $filename;

                        $product->images()->create([
                            'path' => $imagePath,
                            'original_name' => $imageFile->getClientOriginalName(),
                            'mime_type' => $imageFile->getMimeType(),
                            'size' => $imageFile->getSize(),
                            'is_primary' => $isPrimary,
                        ]);
                    }
                }
            }

            // Update primary image for existing images
            if (is_numeric($request->input('primary_image'))) {
                $product->images()->where('id', $request->input('primary_image'))->update(['is_primary' => true]);
            }

            DB::commit();

            // Check if it's an AJAX request (from Dropzone)
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Product updated successfully',
                    'redirect' => route('products.index')
                ]);
            }

            return redirect()
                ->route('products.index')
                ->with('success', 'Product updated successfully.');

        } catch (ValidationException $e) {
            DB::rollBack();

            // Check if it's an AJAX request (from Dropzone)
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $e->errors()
                ], 422);
            }

            return back()
                ->withInput()
                ->withErrors($e->errors());
        } catch (\Exception $e) {
            DB::rollBack();

            // Check if it's an AJAX request (from Dropzone)
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating product: ' . $e->getMessage()
                ], 500);
            }

            return back()
                ->withInput()
                ->withErrors(['error' => 'Error updating product: ' . $e->getMessage()]);
        }
    }

    public function destroy(Product $product)
    {
        try {
            DB::beginTransaction();

            // Delete associated images from public directory
            foreach ($product->images as $image) {
                if ($image->path && file_exists(public_path($image->path))) {
                    unlink(public_path($image->path));
                }
                $image->delete();
            }

            // Delete product
            $product->categories()->detach();
            $product->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error deleting product: ' . $e->getMessage()
            ], 500);
        }
    }
}
