@extends('layouts.admin')

@section('title', 'Edit Category')

@section('breadcrumbs')
    <li class="breadcrumb-item"><a href="{{ route('categories.index') }}">Categories</a></li>
    <li class="breadcrumb-item active">Edit {{ $category->name }}</li>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Edit Category: {{ $category->name }}</h3>
        <div class="card-tools">
            <a href="{{ route('categories.index') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>
    <div class="card-body">
        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <form action="{{ route('categories.update', $category) }}" method="POST" enctype="multipart/form-data" id="categoryForm">
            @csrf
            @method('PUT')
            <input type="hidden" name="remove_images" id="deletedImages" value="">

            <div class="mb-3">
                <label for="name" class="form-label">Name</label>
                <input type="text" class="form-control @error('name') is-invalid @enderror"
                    id="name" name="name" value="{{ old('name', $category->name) }}" required>
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="slug" class="form-label">Slug</label>
                <input type="text" class="form-control @error('slug') is-invalid @enderror"
                    id="slug" name="slug" value="{{ old('slug', $category->slug) }}">
                @error('slug')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text text-muted">Leave empty to auto-generate from name</small>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="editor @error('description') is-invalid @enderror"
                    id="description" name="description">{{ old('description', $category->description) }}</textarea>
                @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="parent_id" class="form-label">Parent Category</label>
                <select class="form-control @error('parent_id') is-invalid @enderror" id="parent_id" name="parent_id">
                    <option value="">None</option>
                    @foreach($categories as $cat)
                        @if($cat->id !== $category->id)
                            <option value="{{ $cat->id }}" {{ old('parent_id', $category->parent_id) == $cat->id ? 'selected' : '' }}>
                                {{ $cat->name }}
                            </option>
                        @endif
                    @endforeach
                </select>
                @error('parent_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-control @error('status') is-invalid @enderror" id="status" name="status" required>
                    <option value="active" {{ old('status', $category->status) == 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ old('status', $category->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
                @error('status')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label class="form-label">Category Images</label>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> <strong>Recommended:</strong> Upload 300 x 300 PNG images for best results.
                    Images will be automatically resized to 300x300 pixels while maintaining aspect ratio.
                </div>
                <div id="existingImages" class="mb-3">
                    @foreach($category->images as $image)
                        <div class="existing-image {{ $image->is_primary ? 'is-primary' : '' }}" data-image-id="{{ $image->id }}">
                            <img src="{{ asset($image->path) }}" alt="{{ $category->name }}"
                                class="img-thumbnail" style="max-width: 300px">
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-danger delete-image">Remove</button>
                                <button type="button" class="btn btn-sm btn-primary set-primary-existing"
                                    {{ $image->is_primary ? 'disabled' : '' }}>
                                    {{ $image->is_primary ? 'Primary Image' : 'Set as Primary' }}
                                </button>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div id="dropzone" class="dropzone"></div>
                <input type="hidden" name="primary_image" id="primaryImage" value="">
                <input type="hidden" name="deleted_images" id="deletedImages" value="">
                @error('images')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>

            <button type="submit" class="btn btn-primary">Update Category</button>
        </form>
    </div>
</div>
@endsection

@section('styles')
    <link href="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone.css" rel="stylesheet" type="text/css" />
    <style>
        .dropzone {
            border: 2px dashed #0087F7;
            border-radius: 5px;
            background: white;
            min-height: 150px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .dropzone .dz-preview.is-primary {
            border: 2px solid #198754;
            border-radius: 5px;
            padding: 5px;
        }

        .dropzone .dz-preview .set-primary-btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        #existingImages {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .existing-image {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }

        .existing-image.is-primary {
            border: 2px solid #198754;
        }
    </style>
@endsection

@push('scripts')
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js"></script>
    <script>
        class MyUploadAdapter {
            constructor(loader) {
                this.loader = loader;
            }

            upload() {
                return this.loader.file
                    .then(file => new Promise((resolve, reject) => {
                        this._uploadFile(file).then(response => {
                            resolve({
                                default: response.url
                            });
                        }).catch(error => {
                            reject(error);
                        });
                    }));
            }

            _uploadFile(file) {
                const formData = new FormData();
                formData.append('upload', file);
                formData.append('_token', '{{ csrf_token() }}');

                return fetch('{{ route("ckeditor.upload") }}', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => response.json())
                    .catch(error => {
                        console.error('Error:', error);
                        throw error;
                    });
            }

            abort() {
                // Abort upload implementation
            }
        }

        function MyCustomUploadAdapterPlugin(editor) {
            editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
                return new MyUploadAdapter(loader);
            };
        }

        ClassicEditor
            .create(document.querySelector('.editor'), {
                extraPlugins: [MyCustomUploadAdapterPlugin],
                toolbar: {
                    items: [
                        'undo', 'redo',
                        '|', 'heading',
                        '|', 'bold', 'italic',
                        '|', 'link', 'uploadImage', 'insertTable', 'mediaEmbed',
                        '|', 'bulletedList', 'numberedList',
                        '|', 'outdent', 'indent'
                    ]
                },
                image: {
                    toolbar: [
                        'imageStyle:inline',
                        'imageStyle:block',
                        'imageStyle:side',
                        '|',
                        'toggleImageCaption',
                        'imageTextAlternative'
                    ]
                },
                height: '300px',
                minHeight: '300px'
            })
            .catch(error => {
                console.error(error);
            });
    </script>

    <!-- Dropzone -->
    <script src="https://unpkg.com/dropzone@6.0.0-beta.1/dist/dropzone-min.js"></script>
    <script>
        // Track deleted images
        let deletedImages = [];

        // Handle existing images
        document.querySelectorAll('.delete-image').forEach(button => {
            button.addEventListener('click', function() {
                const imageContainer = this.closest('.existing-image');
                const imageId = imageContainer.dataset.imageId;
                deletedImages.push(imageId);
                document.getElementById('deletedImages').value = deletedImages.join(',');
                imageContainer.remove();

                // If deleted image was primary, set first remaining image as primary
                if (imageContainer.classList.contains('is-primary')) {
                    const firstRemaining = document.querySelector('.existing-image');
                    if (firstRemaining) {
                        setPrimaryExisting(firstRemaining);
                    }
                }
            });
        });

        document.querySelectorAll('.set-primary-existing').forEach(button => {
            button.addEventListener('click', function() {
                const imageContainer = this.closest('.existing-image');
                setPrimaryExisting(imageContainer);
            });
        });

        function setPrimaryExisting(container) {
            // Remove primary status from all existing images
            document.querySelectorAll('.existing-image').forEach(img => {
                img.classList.remove('is-primary');
                const btn = img.querySelector('.set-primary-existing');
                if (btn) {
                    btn.textContent = 'Set as Primary';
                    btn.disabled = false;
                }
            });

            // Set new primary
            container.classList.add('is-primary');
            const btn = container.querySelector('.set-primary-existing');
            if (btn) {
                btn.textContent = 'Primary Image';
                btn.disabled = true;
            }

            // Update hidden input
            document.getElementById('primaryImage').value = container.dataset.imageId;

            // Remove primary status from dropzone files
            if (myDropzone) {
                myDropzone.files.forEach(file => {
                    if (file.previewElement) {
                        file.previewElement.classList.remove('is-primary');
                        const dzBtn = file.previewElement.querySelector('.set-primary-btn');
                        if (dzBtn) dzBtn.textContent = 'Set as Primary';
                    }
                });
            }
        }

        // Initialize Dropzone
        let myDropzone = new Dropzone("#dropzone", {
            url: "{{ route('categories.update', $category) }}",
            autoProcessQueue: false,
            uploadMultiple: true,
            parallelUploads: 5,
            maxFiles: 5,
            maxFilesize: 2, // MB
            acceptedFiles: ".png, .jpg, .jpeg",
            dictDefaultMessage: "Drop files here or click to upload (300x300 PNG recommended)",
            addRemoveLinks: true,
            paramName: "images",
            init: function() {
                var dz = this;
                var form = document.getElementById('categoryForm');

                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Add method spoofing for PUT request
                    let methodInput = document.createElement('input');
                    methodInput.type = 'hidden';
                    methodInput.name = '_method';
                    methodInput.value = 'PUT';
                    form.appendChild(methodInput);

                    if (dz.getQueuedFiles().length > 0) {
                        dz.processQueue();
                    } else {
                        form.submit();
                    }
                });

                this.on("sending", function(file, xhr, formData) {
                    // Append all form data
                    var formElements = form.elements;
                    for (var i = 0; i < formElements.length; i++) {
                        if (formElements[i].type !== 'file') {
                            formData.append(formElements[i].name, formElements[i].value);
                        }
                    }

                    // Append deleted images if any
                    const deletedImagesValue = document.getElementById('deletedImages').value;
                    if (deletedImagesValue) {
                        formData.append('remove_images', deletedImagesValue);
                    }
                });

                this.on("addedfile", function(file) {
                    const setPrimaryBtn = document.createElement('button');
                    setPrimaryBtn.className = 'btn btn-sm btn-primary set-primary-btn mt-2';
                    setPrimaryBtn.textContent = 'Set as Primary';

                    file.previewElement.appendChild(setPrimaryBtn);

                    setPrimaryBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.setPrimaryImage(file);
                    });
                });

                this.on("success", function(file, response) {
                    window.location.href = "{{ route('categories.index') }}";
                });

                this.on("error", function(file, errorMessage) {
                    console.error('Upload error:', errorMessage);
                });
            },
            setPrimaryImage: function(file) {
                // Remove primary status from all dropzone files
                this.files.forEach(f => {
                    f.isPrimary = false;
                    if (f.previewElement) {
                        f.previewElement.classList.remove('is-primary');
                        const btn = f.previewElement.querySelector('.set-primary-btn');
                        if (btn) btn.textContent = 'Set as Primary';
                    }
                });

                // Remove primary status from existing images
                document.querySelectorAll('.existing-image').forEach(img => {
                    img.classList.remove('is-primary');
                    const btn = img.querySelector('.set-primary-existing');
                    if (btn) {
                        btn.textContent = 'Set as Primary';
                        btn.disabled = false;
                    }
                });

                // Set new primary
                file.isPrimary = true;
                file.previewElement.classList.add('is-primary');
                const btn = file.previewElement.querySelector('.set-primary-btn');
                if (btn) btn.textContent = 'Primary Image';

                // Update hidden input
                document.getElementById('primaryImage').value = this.files.indexOf(file);
            }
        });
    </script>
@endpush
