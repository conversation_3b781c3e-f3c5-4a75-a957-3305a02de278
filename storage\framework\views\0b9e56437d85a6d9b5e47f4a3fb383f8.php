<?php $__env->startSection('title', 'Welcome to Our Furniture Store'); ?>
<?php $__env->startSection('meta_description', 'Discover our exclusive collection of high-quality furniture for your home. Find sofas, beds, dining sets, and more at competitive prices.'); ?>
<?php $__env->startSection('meta_keywords', 'furniture, home decor, sofas, beds, dining sets, chairs, tables'); ?>

<?php $__env->startSection('content'); ?>
    <main class="main">

        <!-- hero slider -->
        <div class="hero-section hs-3">
            <div class="container-fluid px-0">
                <div class="hero-slider owl-carousel owl-theme">
                    <?php $__empty_1 = true; $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="hero-single">
                            <div class="hero-single-bg" style="background-image: url(<?php echo e(asset($banner->image_link)); ?>)"></div>
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col-md-12 col-lg-8 col-xl-6">
                                        <div class="hero-content">
                                            <h6 class="hero-sub-title" data-animation="fadeInUp" data-delay=".25s">
                                                <?php echo e($banner->name); ?>

                                            </h6>
                                            <h1 class="hero-title" data-animation="fadeInRight" data-delay=".50s">
                                                <?php echo e($banner->title); ?>

                                            </h1>
                                            <?php if($banner->description): ?>
                                                <p data-animation="fadeInLeft" data-delay=".75s">
                                                    <?php echo e($banner->description); ?>

                                                </p>
                                            <?php endif; ?>
                                            <div class="hero-btn" data-animation="fadeInUp" data-delay="1s">
                                                <?php if($banner->shop_now_link): ?>
                                                    <a href="<?php echo e($banner->shop_now_link); ?>" class="theme-btn">Shop Now<i
                                                            class="fas fa-arrow-right"></i></a>
                                                <?php endif; ?>
                                                <a href="<?php echo e(route('about')); ?>" class="theme-btn theme-btn2">Learn More<i
                                                        class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <!-- Default banners if no banners are available in the database -->
                        <div class="hero-single">
                            <div class="hero-single-bg"
                                style="background-image: url(<?php echo e(asset('assets/img/hero/slider-1.jpg')); ?>)"></div>
                            <div class="container">
                                <div class="row align-items-center">
                                    <div class="col-md-12 col-lg-8 col-xl-6">
                                        <div class="hero-content">
                                            <h6 class="hero-sub-title" data-animation="fadeInUp" data-delay=".25s">Welcome
                                                to Furniture
                                                !</h6>
                                            <h1 class="hero-title" data-animation="fadeInRight" data-delay=".50s">
                                                Discover modern <span>furniture</span> for your every room
                                            </h1>
                                            <p data-animation="fadeInLeft" data-delay=".75s">
                                                There are many variations of passages orem psum available but the majority
                                                have
                                                suffered are going to use a passage alteration in some form by injected
                                                humour.
                                            </p>
                                            <div class="hero-btn" data-animation="fadeInUp" data-delay="1s">
                                                <a href="#" class="theme-btn">Shop Now<i class="fas fa-arrow-right"></i></a>
                                                <a href="<?php echo e(route('about')); ?>" class="theme-btn theme-btn2">Learn More<i
                                                        class="fas fa-arrow-right"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <!-- hero slider end -->

        <!-- category area -->
        <div class="category-area3 pt-50">
            <div class="container wow fadeInUp" data-wow-delay=".25s">
                <div class="category-slider owl-carousel owl-theme">
                    <?php
    echo "Categories here";
    print_r($categories);
    die;
                        ?>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="category-item">
                            <a href="<?php echo e(route('category_products', $category->slug)); ?>">
                                <div class="category-info">
                                    <div class="icon">
                                        <?php if($category->images->isNotEmpty()): ?>
                                            <img src="<?php echo e(asset($category->images->first()->path)); ?>" alt="<?php echo e($category->name); ?>">
                                        <?php else: ?>
                                            <img src="<?php echo e(asset('assets/img/category/default.png')); ?>" alt="<?php echo e($category->name); ?>">
                                        <?php endif; ?>
                                    </div>
                                    <div class="content">
                                        <h4><?php echo e($category->name); ?></h4>
                                        <p><?php echo e($category->products_count); ?> Items</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        <!-- category area end-->

        <!-- listings sections -->
        <?php $__currentLoopData = $listings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $listing): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="product-area pb-50 mt-5">
                <div class="container">
                    <div class="row">
                        <div class="col-12 wow fadeInDown" data-wow-delay=".25s">
                            <div class="site-heading-inline">
                                <h2 class="site-title"><?php echo e($listing->name); ?></h2>
                                <a href="<?php echo e(route('listing_products', $listing->slug)); ?>">View More <i
                                        class="fas fa-angle-double-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="product-wrap item-2 wow fadeInUp" data-wow-delay=".25s">
                        <div class="product-slider owl-carousel owl-theme">
                            <?php $__currentLoopData = $listing->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="product-item">
                                    <div class="product-img">
                                        <?php if($product->price < 200): ?>
                                            <span class="type discount">Sale</span>
                                        <?php elseif($loop->first): ?>
                                            <span class="type new">New</span>
                                        <?php elseif($loop->iteration == 2): ?>
                                            <span class="type hot">Hot</span>
                                        <?php endif; ?>
                                        <a href="<?php echo e(route('product_view', $product->slug)); ?>">
                                            <?php if($product->images->isNotEmpty()): ?>
                                                <img src="<?php echo e(asset($product->images->first()->path)); ?>" alt="<?php echo e($product->name); ?>">
                                            <?php else: ?>
                                                <img src="<?php echo e(asset('assets/img/product/01.png')); ?>" alt="<?php echo e($product->name); ?>">
                                            <?php endif; ?>
                                        </a>
                                        <div class="product-action-wrap">
                                            <div class="product-action">
                                                <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                                    data-tooltip="tooltip" title="Quick View"><i class="far fa-eye"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="product-content">
                                        <h3 class="product-title"><a
                                                href="<?php echo e(route('product_view', $product->slug)); ?>"><?php echo e($product->name); ?></a></h3>
                                        <div class="product-rate">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                        <div class="product-bottom">
                                            <div class="product-price">
                                                <span>$<?php echo e(number_format($product->price, 2)); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <!-- listings sections end -->

        <!-- choose-area -->
        <div class="choose-area pb-100">
            <div class="container">
                <div class="row g-4 align-items-center wow fadeInDown" data-wow-delay=".25s">
                    <div class="col-lg-4">
                        <span class="site-title-tagline">Why Choose Us</span>
                        <h2 class="site-title">We Provide Premium Quality Furniture For You</h2>
                    </div>
                    <div class="col-lg-4">
                        <p>There are many variations of passages available but the majority have suffered you are going
                            to use a passage you need to be sure alteration in some form by injected humour randomised
                            words even slightly believable.</p>
                    </div>
                    <div class="col-lg-4">
                        <div class="choose-img">
                            <img src="<?php echo e(asset('assets/img/choose/01.jpg')); ?>" alt="">
                        </div>
                    </div>
                </div>
                <div class="choose-content wow fadeInUp" data-wow-delay=".25s">
                    <div class="row g-4">
                        <div class="col-lg-4">
                            <div class="choose-item">
                                <div class="choose-icon">
                                    <img src="<?php echo e(asset('assets/img/icon/warranty.svg')); ?>" alt="">
                                </div>
                                <div class="choose-info">
                                    <h4>3 Years Warranty</h4>
                                    <p>It is a long established fact that a reader will be distracted by the readable
                                        content of a page when looking at its layout.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="choose-item">
                                <div class="choose-icon">
                                    <img src="<?php echo e(asset('assets/img/icon/price.svg')); ?>" alt="">
                                </div>
                                <div class="choose-info">
                                    <h4>Affordable Price</h4>
                                    <p>It is a long established fact that a reader will be distracted by the readable
                                        content of a page when looking at its layout.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="choose-item">
                                <div class="choose-icon">
                                    <img src="<?php echo e(asset('assets/img/icon/delivery.svg')); ?>" alt="">
                                </div>
                                <div class="choose-info">
                                    <h4>Free Shipping</h4>
                                    <p>It is a long established fact that a reader will be distracted by the readable
                                        content of a page when looking at its layout.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- choose-area end-->

    </main>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.front', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp82\htdocs\furnitureecom\resources\views/pages/home.blade.php ENDPATH**/ ?>